name: Code Quality Checks

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  code-quality:
    name: Code Quality Checks
    runs-on: ubuntu-24.04

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: "Set up Python"
        uses: actions/setup-python@v5
        with:
          python-version-file: ".python-version"

      - name: Install uv
        uses: astral-sh/setup-uv@v6

      - name: Install dependencies
        run: uv sync

      - name: Check linting
        run: uv run ruff check src/

      - name: Check formatting
        run: uv run ruff format --check src/

      - name: Check Bandit Security Scan
        run: uv run bandit -r src/

      - name: Check Dependency Audit
        run: uv run pip-audit -l