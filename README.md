# Snake Game

A simple snake game built with Python and Pygame, now refactored with proper type hints and modular structure.

## Project Structure

```
snake-game/
├── main.py              # Entry point
├── snake_game/          # Main package
│   └── src/             # Source code package
│       ├── __init__.py  # Package initialization
│       ├── models.py    # Game models (Snake, Cube classes)
│       ├── utils.py     # Utility functions
│       └── game.py      # Main game logic
├── tests/               # Comprehensive test suite
│   ├── __init__.py      # Test package initialization
│   ├── conftest.py      # Pytest configuration and fixtures
│   ├── test_models.py   # Model tests (31 tests)
│   ├── test_utils.py    # Utility tests (18 tests)
│   ├── test_game.py     # Game tests (7 tests)
│   └── test_integration.py # Integration tests (12 tests)
├── run_tests.py         # Custom test runner
├── pytest.ini          # Pytest configuration
├── TEST_SUMMARY.md      # Detailed test documentation
├── requirements.txt     # Dependencies
└── README.md           # This file
```

## Key Improvements

- ✅ **Type Hints**: Added comprehensive type hints to all functions, methods, and variables
- ✅ **Modular Structure**: Organized code into logical modules in `src/` folder
- ✅ **Separation of Concerns**:
  - `models.py`: Game entities (Snake, Cube)
  - `utils.py`: Helper functions
  - `game.py`: Main game loop
- ✅ **Better Naming**: Improved class names (e.g., `Cube` instead of `cube`)
- ✅ **Documentation**: Added docstrings for all classes and functions
- ✅ **Fixed Issues**:
  - Resolved class-level variable problems in original code
  - Fixed pygame initialization error by separating input handling from movement
  - Added backwards movement prevention

## Running the Game

```bash
python main.py
```

## Running Tests

### Comprehensive Test Suite (68 Tests)

```bash
# Run all tests
python -m pytest tests/ -v

# Run specific test modules
python -m pytest tests/test_models.py -v      # Model tests (31 tests)
python -m pytest tests/test_utils.py -v       # Utility tests (18 tests)
python -m pytest tests/test_game.py -v        # Game tests (7 tests)
python -m pytest tests/test_integration.py -v # Integration tests (12 tests)

# Run with custom test runner
python run_tests.py

# Run with coverage reporting
python run_tests.py --coverage
```

### Test Coverage
- **✅ 68 Tests Total - All Passing**
- **Models**: 31 tests covering Cube and Snake classes
- **Utils**: 18 tests covering utility functions
- **Game**: 7 tests covering main game logic
- **Integration**: 12 tests covering cross-module interactions

See [TEST_SUMMARY.md](TEST_SUMMARY.md) for detailed test documentation.

## Dependencies

- pygame
- tkinter (usually included with Python)

## How to Run this Locally

### Step 1: Create a Virtual Environment
```bash
cd /path/to/folder
mkdir snake
cd snake
python -m venv .
source bin/activate  # On Windows: Scripts\activate
```

### Step 2: Install Dependencies
```bash
pip install -r requirements.txt
python main.py
```
