# Snake Game

A simple snake game built with Python and Pygame, now refactored with proper type hints and modular structure.

## Project Structure

```
snake-game/
├── main.py              # Entry point
├── src/                 # Source code package
│   ├── __init__.py      # Package initialization
│   ├── models.py        # Game models (Snake, Cube classes)
│   ├── utils.py         # Utility functions
│   └── game.py          # Main game logic
├── test_refactored.py   # Tests for refactored code
├── requirements.txt     # Dependencies
└── README.md           # This file
```

## Key Improvements

- ✅ **Type Hints**: Added comprehensive type hints to all functions, methods, and variables
- ✅ **Modular Structure**: Organized code into logical modules in `src/` folder
- ✅ **Separation of Concerns**:
  - `models.py`: Game entities (Snake, Cube)
  - `utils.py`: Helper functions
  - `game.py`: Main game loop
- ✅ **Better Naming**: Improved class names (e.g., `Cube` instead of `cube`)
- ✅ **Documentation**: Added docstrings for all classes and functions
- ✅ **Fixed Issues**: Resolved class-level variable problems in original code

## Running the Game

```bash
python main.py
```

## Running Tests

```bash
python test_refactored.py
```

## Dependencies

- pygame
- tkinter (usually included with Python)

## How to Run this Locally

### Step 1: Create a Virtual Environment
```bash
cd /path/to/folder
mkdir snake
cd snake
python -m venv .
source bin/activate  # On Windows: Scripts\activate
```

### Step 2: Install Dependencies
```bash
pip install -r requirements.txt
python main.py
```
