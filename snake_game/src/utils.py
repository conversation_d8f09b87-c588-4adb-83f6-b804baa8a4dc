"""
Utility functions for the Snake Game.
"""

import random
import tkinter as tk
from tkinter import messagebox
from typing import <PERSON>ple, List
import pygame

from .models import Snake, Cube


def draw_grid(w: int, rows: int, surface: pygame.Surface) -> None:
    """
    Draw grid lines on the game surface (currently commented out).
    
    Args:
        w: Width of the game window
        rows: Number of rows in the grid
        surface: Pygame surface to draw on
    """
    sizebtwn: int = w // rows
    x: int = 0
    y: int = 0

    for i in range(rows):
        x += sizebtwn
        y += sizebtwn
        # pygame.draw.line(surface, (255, 255, 255), (x, 0), (x, w))
        # pygame.draw.line(surface, (255, 255, 255), (0, y), (w, y))


def random_snack(rows: int, snake: Snake) -> Tuple[int, int]:
    """
    Generate a random position for the snack that doesn't overlap with the snake.
    
    Args:
        rows: Number of rows in the grid
        snake: The snake object to avoid collision with
        
    Returns:
        Tuple of (x, y) coordinates for the snack
    """
    positions: List[Cube] = snake.body
    
    while True:
        x: int = random.randrange(rows)
        y: int = random.randrange(rows)
        
        # Check if the position overlaps with any part of the snake
        if len(list(filter(lambda cube: cube.pos == (x, y), positions))) > 0:
            continue
        else:
            break
            
    return x, y


def message_box(subject: str, context: str) -> None:
    """
    Display a message box with the given subject and context.
    
    Args:
        subject: Title of the message box
        context: Content of the message box
    """
    root: tk.Tk = tk.Tk()
    root.attributes('-topmost', True)
    root.withdraw()
    messagebox.showinfo(subject, context)
    
    try:
        root.destroy()
    except Exception:
        pass


def redraw_window(
    surface: pygame.Surface, 
    snake: Snake, 
    snack: Cube, 
    width: int, 
    rows: int
) -> None:
    """
    Redraw the entire game window.
    
    Args:
        surface: Pygame surface to draw on
        snake: The snake object to draw
        snack: The snack cube to draw
        width: Width of the game window
        rows: Number of rows in the grid
    """
    surface.fill((0, 0, 0))
    snake.draw(surface)
    snack.draw(surface)
    draw_grid(width, rows, surface)
    pygame.display.update()
